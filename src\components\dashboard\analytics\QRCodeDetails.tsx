import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Button } from '../../ui/button';
import { 
  QrCode,
  Calendar,
  Eye,
  ExternalLink,
  Copy,
  Globe,
  Mail,
  Wifi,
  FileText,
  Phone,
  MapPin,
  RefreshCw
} from 'lucide-react';
import { formatDateInUserTimezone, formatTimeAgoInUserTimezone } from '../../../lib/api-utils';

interface QRCodeDetailsProps {
  qrCodeId: string;
}

interface QRCodeInfo {
  id: string;
  name: string;
  content_type: string;
  data: string;
  original_url?: string;
  email_address?: string;
  wifi_ssid?: string;
  phone_number?: string;
  custom_slug?: string;
  dynamic: boolean;
  created_at: string;
  total_scans: number;
  recent_scans_24h: number;
  last_scan_time?: string;
  tracking_domain?: string;
}

export const QRCodeDetails: React.FC<QRCodeDetailsProps> = ({ qrCodeId }) => {
  const [qrCodeInfo, setQRCodeInfo] = useState<QRCodeInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const fetchQRCodeDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/qr-codes/${qrCodeId}/details`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch QR code details');
      }

      const data = await response.json();
      setQRCodeInfo(data as unknown as QRCodeInfo);
      setError(null);
    } catch (err) {
      console.error('Error fetching QR code details:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch QR code details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQRCodeDetails();
  }, [qrCodeId]);

  const getContentTypeIcon = (contentType: string) => {
    switch (contentType.toLowerCase()) {
      case 'url':
        return <Globe className="h-5 w-5" />;
      case 'email':
        return <Mail className="h-5 w-5" />;
      case 'wifi':
        return <Wifi className="h-5 w-5" />;
      case 'phone':
        return <Phone className="h-5 w-5" />;
      case 'text':
        return <FileText className="h-5 w-5" />;
      case 'location':
        return <MapPin className="h-5 w-5" />;
      default:
        return <QrCode className="h-5 w-5" />;
    }
  };

  const getContentTypeColor = (contentType: string) => {
    switch (contentType.toLowerCase()) {
      case 'url':
        return 'bg-blue-100 text-blue-800';
      case 'email':
        return 'bg-green-100 text-green-800';
      case 'wifi':
        return 'bg-purple-100 text-purple-800';
      case 'phone':
        return 'bg-orange-100 text-orange-800';
      case 'text':
        return 'bg-gray-100 text-gray-800';
      case 'location':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCopyData = async () => {
    if (!qrCodeInfo?.original_url) return;
    
    try {
      await navigator.clipboard.writeText(qrCodeInfo.original_url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  // Use timezone-aware formatting functions from api-utils

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
            <div className="lg:col-span-2 space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
            <div className="space-y-4">
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !qrCodeInfo) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <QrCode className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load QR Code Details</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={fetchQRCodeDetails} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <QrCode className="h-6 w-6" />
          <span>QR Code Details</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
          {/* QR Code Information */}
          <div className="lg:col-span-2 space-y-6">
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">{qrCodeInfo.name}</h3>
                <div className="flex items-center space-x-2">
                  <Badge className={getContentTypeColor(qrCodeInfo.content_type)}>
                    {getContentTypeIcon(qrCodeInfo.content_type)}
                    <span className="ml-1 capitalize">{qrCodeInfo.content_type}</span>
                  </Badge>
                  {qrCodeInfo.dynamic && (
                    <Badge variant="outline" className="text-blue-700 border-blue-200">
                      Dynamic
                    </Badge>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-700">Content</p>
                    <p className="text-sm text-gray-900 truncate" title={qrCodeInfo.data}>
                    {qrCodeInfo.original_url}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyData}
                      className="flex items-center space-x-1"
                    >
                      <Copy className="h-3 w-3" />
                      <span>{copied ? 'Copied!' : 'Copy'}</span>
                    </Button>
                    {qrCodeInfo.content_type === 'url' && qrCodeInfo.original_url && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(qrCodeInfo.original_url, '_blank')}
                        className="flex items-center space-x-1"
                      >
                        <ExternalLink className="h-3 w-3" />
                        <span>Visit</span>
                      </Button>
                    )}
                  </div>
                </div>

                {qrCodeInfo.custom_slug && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm font-medium text-blue-700">Custom Slug</p>
                    <p className="text-sm text-blue-900">https://{qrCodeInfo.tracking_domain || 'qr.qranalytica.com'}/{qrCodeInfo.custom_slug}</p>
                  </div>
                )}

                <div className="grid gap-3 grid-cols-1 sm:grid-cols-2">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-600" />
                      <p className="text-sm font-medium text-gray-700">Created</p>
                    </div>
                    <p className="text-sm text-gray-900 mt-1">{formatDateInUserTimezone(qrCodeInfo.created_at)}</p>
                  </div>

                  {qrCodeInfo.last_scan_time && (
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Eye className="h-4 w-4 text-gray-600" />
                        <p className="text-sm font-medium text-gray-700">Last Scan</p>
                      </div>
                      <p className="text-sm text-gray-900 mt-1">{formatTimeAgoInUserTimezone(qrCodeInfo.last_scan_time)}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700">Total Scans</p>
                  <p className="text-2xl font-bold text-blue-900">{qrCodeInfo.total_scans.toLocaleString()}</p>
                </div>
                <div className="p-3 bg-blue-200 rounded-full">
                  <Eye className="h-6 w-6 text-blue-700" />
                </div>
              </div>
            </div>

            <div className="p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-700">Last 24 Hours</p>
                  <p className="text-2xl font-bold text-green-900">{qrCodeInfo.recent_scans_24h.toLocaleString()}</p>
                </div>
                <div className="p-3 bg-green-200 rounded-full">
                  <Calendar className="h-6 w-6 text-green-700" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QRCodeDetails;
