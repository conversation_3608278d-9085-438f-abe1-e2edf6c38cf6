import QRCodeStyling from "qr-code-styling";
import { v4 as uuidv4 } from "uuid";
import { validateQROptions } from "../constants/qrDefaults";

// Types for QR download functionality
export interface QRDownloadOptions {
  qrOptions: any;
  qrCodeName?: string;
  isDynamic?: boolean;
  customUrl?: string;
  selectedDomain?: string;
  userEmail?: string;
  contentType?: string;
  formData?: any;
  onProgress?: (progress: { isDownloading: boolean; step: string; progress: number }) => void;
}

export interface QRCodeData {
  id: string;
  name: string | null;
  data: string;
  content_type: string;
  original_url?: string | null;
  custom_slug?: string | null;
  tracking_domain?: string | null;
}

// Helper function to convert data URL to blob (from QRGenerator)
const dataURLtoBlob = (dataUrl: string): Blob => {
  const [header, base64] = dataUrl.split(",");
  const mimeMatch = header.match(/data:(.*);base64/);
  const mime = mimeMatch ? mimeMatch[1] : "image/png";
  const binary = atob(base64);
  const array = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) array[i] = binary.charCodeAt(i);
  return new Blob([array], { type: mime });
};

// Helper function to get content name based on type and data
const getContentName = (contentType: string, formData: any, originalData: string): string => {
  switch (contentType) {
    case "url":
      return originalData;
    case "whatsapp":
      return `WhatsApp ${formData?.whatsapp?.phoneNumber || 'Contact'}`;
    case "email":
      return `Email ${formData?.email?.email || 'Contact'}`;
    case "wifi":
      return `WiFi ${formData?.wifi?.ssid || 'Network'}`;
    default:
      return originalData || "QR Code";
  }
};

/**
 * Download QR code using stored options from database
 * This function replicates the QRGenerator download logic for dashboard use
 */
export const downloadQRCodeFromDatabase = async (
  qrCodeData: QRCodeData,
  onProgress?: (progress: { isDownloading: boolean; step: string; progress: number }) => void
): Promise<void> => {
  try {
    onProgress?.({ isDownloading: true, step: "Starting download...", progress: 10 });

    // Parse stored QR options from database
    let qrOptions: any;
    try {
      qrOptions = typeof qrCodeData.data === 'string' ? JSON.parse(qrCodeData.data) : qrCodeData.data;
    } catch (error) {
      console.error("Failed to parse QR options from database:", error);
      throw new Error("Invalid QR code data format");
    }

    onProgress?.({ isDownloading: true, step: "Preparing QR options...", progress: 30 });

    // Validate and prepare options
    let updatedOptions = { ...qrOptions };

    // Handle logo upload if needed (data URL to hosted URL)
    if (updatedOptions.image && updatedOptions.image.startsWith("data:")) {
      onProgress?.({ isDownloading: true, step: "Uploading logo...", progress: 40 });
      try {
        const logoBlob = dataURLtoBlob(updatedOptions.image);
        const form = new FormData();
        form.append("file", logoBlob, `logo-${uuidv4()}.png`);
        const res = await fetch("/api/upload-logo", { method: "POST", body: form });
        if (res.ok) {
          const uploadRes = (await res.json()) as { url: string };
          updatedOptions.image = uploadRes.url;
        }
      } catch (err) {
        console.warn("Logo upload failed, proceeding without logo:", err);
        updatedOptions.image = "";
      }
    }

    // Determine the final data URL for the QR code
    let finalDataUrl = "";
    if (qrCodeData.custom_slug && qrCodeData.tracking_domain) {
      // Dynamic QR with tracking URL
      finalDataUrl = `https://${qrCodeData.tracking_domain}/${qrCodeData.custom_slug}`;
    } else if (qrCodeData.original_url) {
      // Static QR with original URL
      finalDataUrl = qrCodeData.original_url;
    } else {
      // Fallback to any data in the options
      finalDataUrl = updatedOptions.data || "";
    }

    // Update options with final data
    updatedOptions.data = finalDataUrl;

    onProgress?.({ isDownloading: true, step: "Generating QR code...", progress: 70 });

    // Generate high-resolution QR code for download
    const highResOptions = validateQROptions({ 
      ...updatedOptions, 
      width: 2400, 
      height: 2400 
    });

    const qrCodeInstance = new QRCodeStyling(highResOptions);
    
    // Generate filename
    const filename = qrCodeData.name || `qr-code-${qrCodeData.id}`;

    onProgress?.({ isDownloading: true, step: "Downloading...", progress: 90 });

    // Download the QR code
    qrCodeInstance.download({ extension: "png", name: filename });

    onProgress?.({ isDownloading: true, step: "Complete!", progress: 100 });

    // Reset progress after a short delay
    setTimeout(() => {
      onProgress?.({ isDownloading: false, step: "", progress: 0 });
    }, 1500);

  } catch (error) {
    console.error("QR download failed:", error);
    onProgress?.({ isDownloading: false, step: "", progress: 0 });
    throw error;
  }
};

/**
 * Download QR code with custom options (for new QR generation)
 * This function replicates the QRGenerator download logic for custom QR creation
 */
export const downloadQRCodeWithOptions = async (
  options: QRDownloadOptions
): Promise<void> => {
  const { 
    qrOptions, 
    qrCodeName, 
    isDynamic, 
    customUrl, 
    selectedDomain, 
    userEmail, 
    contentType, 
    formData,
    onProgress 
  } = options;

  try {
    onProgress?.({ isDownloading: true, step: "Starting download...", progress: 10 });

    let updatedOptions = { ...qrOptions };
    let trackingUrl = "";

    // Step 1: Upload logo if needed
    onProgress?.({ isDownloading: true, step: "Uploading logo...", progress: 20 });
    if (updatedOptions.image && updatedOptions.image.startsWith("data:")) {
      try {
        const logoBlob = dataURLtoBlob(updatedOptions.image);
        const form = new FormData();
        form.append("file", logoBlob, `logo-${uuidv4()}.png`);
        const res = await fetch("/api/upload-logo", { method: "POST", body: form });
        if (res.ok) {
          const uploadRes = (await res.json()) as { url: string };
          updatedOptions.image = uploadRes.url;
        }
      } catch (err) {
        console.error("Logo upload failed", err);
      }
    }

    // Step 2: Store QR code and generate tracking URL if analytics enabled
    onProgress?.({ isDownloading: true, step: "Generating tracking URL...", progress: 40 });
    
    if (isDynamic && userEmail) {
      // Only store in database for dynamic QR codes with analytics
      try {
        const storeResponse = await fetch("/api/store-qr", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            name: qrCodeName || getContentName(contentType || "url", formData, updatedOptions.data) || "qr-code",
            options: updatedOptions,
            customUrl: customUrl,
            isDynamic: isDynamic,
            domain: selectedDomain || "qr.qranalytica.com",
            userEmail: userEmail,
            contentType: contentType,
            formData: formData,
          }),
        });

        const storeData: any = await storeResponse.json();
        if (storeResponse.ok && storeData.custom_slug) {
          // Generate tracking URL using the domain and custom slug
          const domain = selectedDomain || "qr.qranalytica.com";
          trackingUrl = `https://${domain}/${storeData.custom_slug}`;
          
          // Update QR options with tracking URL
          updatedOptions.data = trackingUrl;
        } else {
          console.error("Failed to store QR code:", storeData.error);
        }
      } catch (err) {
        console.error("Error storing QR code:", err);
      }
    } else {
      // Static QR - no database storage needed, just update progress
      onProgress?.({ isDownloading: true, step: "Preparing download...", progress: 50 });
    }

    // Step 3: Generate high-resolution QR code
    onProgress?.({ isDownloading: true, step: "Generating QR code...", progress: 70 });
    
    // Step 4: Download high-res PNG
    onProgress?.({ isDownloading: true, step: "Downloading...", progress: 90 });
    
    const highResOptions = validateQROptions({ ...updatedOptions, width: 2400, height: 2400 });
    const temp = new QRCodeStyling(highResOptions);
    temp.download({ extension: "png", name: qrCodeName || "qr-code" });

    // Complete
    onProgress?.({ isDownloading: true, step: "Complete!", progress: 100 });
    
    // Reset progress after a short delay
    setTimeout(() => {
      onProgress?.({ isDownloading: false, step: "", progress: 0 });
    }, 1500);

  } catch (error) {
    console.error("Download failed:", error);
    onProgress?.({ isDownloading: false, step: "", progress: 0 });
    throw error;
  }
};
